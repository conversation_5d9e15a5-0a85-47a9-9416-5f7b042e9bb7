import os
import anthropic
from dotenv import load_dotenv

load_dotenv()

def read_file_content(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def generate_document(brd_content, prd_content, brand_name, document_type):
    """
    Generates a specific document using the Anthropic API.
    """
    anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
    if not anthropic_api_key:
        raise ValueError("ANTHROPIC_API_KEY not found in .env file")

    client = anthropic.Anthropic(api_key=anthropic_api_key)

    prompt = f"""
    **Brand Name:** {brand_name}

    **Business Requirements Document (BRD) Content:**
    {brd_content}

    **Product Requirements Document (PRD) Content:**
    {prd_content}

    Based on the above information, please generate ONLY the following document: **{document_type}**.
    Do not include any other documents or introductory text.
    """

    message = client.messages.create(
        model="claude-3-haiku-20240307",
        max_tokens=4000,
        temperature=0.5,
        messages=[
            {
                "role": "user",
                "content": prompt
            }
        ]
    )

    if message.content and len(message.content) > 0 and hasattr(message.content[0], 'text'):
        return message.content[0].text
    return ""

def save_document(content, file_path):
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

if __name__ == "__main__":
    brd_content = read_file_content('brd.md')
    prd_content = read_file_content('prd.md')
    brand_name = read_file_content('results/chosen_brand_name.txt')

    print("Generating Software Requirements Specification (SRS)...")
    srs_content = generate_document(brd_content, prd_content, brand_name, "Software Requirements Specification (SRS)")
    save_document(srs_content, 'results/srs.md')
    print("SRS saved to results/srs.md")

    print("Generating System Architecture...")
    system_architecture_content = generate_document(brd_content, prd_content, brand_name, "System Architecture")
    save_document(system_architecture_content, 'results/system_architecture.md')
    print("System Architecture saved to results/system_architecture.md")

    print("Generating User Flow...")
    user_flow_content = generate_document(brd_content, prd_content, brand_name, "User Flow (in Mermaid syntax)")
    save_document(user_flow_content, 'results/user_flow.md')
    print("User Flow saved to results/user_flow.md")

    print("\nSuccessfully generated all documents.")