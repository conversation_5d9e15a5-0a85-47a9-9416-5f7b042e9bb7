import os
import subprocess
import shutil

def read_file_content(file_path):
    """Reads and returns the content of a file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def create_next_app(project_path):
    """Creates a new Next.js app using the standard create-next-app command."""
    if os.path.exists(project_path):
        print(f"Directory '{project_path}' already exists. Skipping Next.js app creation.")
        return

    parent_dir = os.path.dirname(project_path)
    project_name = os.path.basename(project_path)

    print(f"Creating Next.js app: {project_name} in {parent_dir}...")
    # This command creates a new Next.js app with TypeScript, Tailwind CSS, and ESLint.
    # Note: create-next-app can be interactive.
    command = f"npx create-next-app@latest {project_name} --typescript --tailwind --eslint --app --import-alias "@/*" --no-src-dir --use-npm --yes"
    
    try:
        subprocess.run(command, check=True, shell=True, cwd=parent_dir, input='n\n', text=True)
        print("Next.js app created successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error creating Next.js app: {e}")
        print("Please ensure Node.js, npm, and npx are installed and in your PATH.")
        exit(1)



def move_srs_file(project_path, srs_file_path):
    """Copies the SRS file to the new project directory."""
    destination_path = os.path.join(project_path, 'srs.md')
    print(f"Copying srs.md to {destination_path}...")
    shutil.copy(srs_file_path, destination_path)
    print("srs.md copied.")

def create_full_context_file(project_path, brd, prd, srs, brand_name):
    """Creates a comprehensive context file for the AI developer."""
    print("Creating GEMINI_CONTEXT.md file...")
    context_content = f"""# Project Context: {brand_name}

This document contains all the necessary information to develop the MVP for {brand_name}.

## 1. Brand Name

{brand_name}

## 2. Business Requirements Document (BRD)

{brd}

## 3. Product Requirements Document (PRD)

{prd}

## 4. Software Requirements Specification (SRS)

{srs}
"""
    context_file_path = os.path.join(project_path, 'GEMINI_CONTEXT.md')
    with open(context_file_path, 'w', encoding='utf-8') as f:
        f.write(context_content)
    print(f"GEMINI_CONTEXT.md created in {project_path}")

if __name__ == "__main__":
    omniflow_dir = os.getcwd()
    code_base_dir = os.path.dirname(omniflow_dir)

    # 1. Read the brand name to use for the app directory
    brand_name = read_file_content(os.path.join(omniflow_dir, 'results', 'chosen_brand_name.txt')).strip()
    project_name = brand_name.lower().replace(' ', '_').replace('-', '_')
    project_path = os.path.join(omniflow_dir, project_name)

    print(f"--- Starting Stage 4: MVP Development Setup for '{brand_name}' ---")
    print(f"Project will be created at: {project_path}")

    # 2. Create the Next.js app
    create_next_app(project_path)

    

    # 4. Move the srs.md file into the app's directory
    srs_file_path = os.path.join(omniflow_dir, 'results', 'srs.md')
    move_srs_file(project_path, srs_file_path)

    # 5. Create a full context file
    brd_content = read_file_content(os.path.join(omniflow_dir, 'brd.md'))
    prd_content = read_file_content(os.path.join(omniflow_dir, 'prd.md'))
    srs_content = read_file_content(srs_file_path)
    create_full_context_file(project_path, brd_content, prd_content, srs_content, brand_name)

    print("\n--- Stage 4 Setup Complete ---")
    print(f"Next steps: Navigate to the new project directory and start development:")
    print(f"cd {project_path}")
