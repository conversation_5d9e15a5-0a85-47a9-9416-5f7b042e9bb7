# main.py
import os
import json
import anthropic # Import the Anthropic library for Claude API calls
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# --- Configuration ---
# Initialize the Anthropic client
# It will automatically pick up ANTHROPIC_API_KEY from environment variables loaded by dotenv
client = anthropic.Anthropic(
    api_key=os.getenv("ANTHROPIC_API_KEY"),
)

# --- Helper Functions for Claude API Calls ---

def call_claude_name_generation_api(business_idea: str) -> list[str]:
    """
    Calls the Anthropic Claude API to generate a list of 10 brand names.
    Returns a list of strings.
    """
    # Using Claude 3.5 Sonnet, which is the latest recommended Sonnet model
    model_name = "claude-3-5-sonnet-20240620" 
    
    # Prompt for <PERSON> to generate a JSON array of names
    prompt_messages = [
        {"role": "user", "content": (
            f"Generate a JSON array of 10 unique, creative, and relevant brand names for a "
            f"SaaS business focused on: {business_idea}. "
            f"The JSON should contain only the array of strings, like [\"Name1\", \"Name2\", ...]."
            f"\n\nRespond only with the JSON array, no conversational text."
        )}
    ]
    
    try:
        response = client.messages.create(
            model=model_name,
            max_tokens=1000, # Set an appropriate max_tokens
            messages=prompt_messages,
            # Claude's API has a `response_format` parameter for JSON, similar to OpenAI
            # However, for simple JSON arrays, instructing in the prompt usually works well.
            # If strict JSON parsing fails, you might need more robust parsing or prompt engineering.
        )
        
        # The response.content is a list of ContentBlock objects.
        # For a text response, we usually get one ContentBlock with type 'text'.
        json_string_response = response.content[0].text
        
        # Attempt to parse the JSON string into a Python list
        parsed_response = json.loads(json_string_response)
        
        # Ensure the parsed response is a list of strings
        if isinstance(parsed_response, list) and all(isinstance(n, str) for n in parsed_response):
            return parsed_response
        else:
            print(f"Claude returned unexpected JSON structure or non-string elements: {json_string_response}")
            return [f"Error parsing names from Claude response: {json_string_response}"]
            
    except json.JSONDecodeError:
        print(f"Failed to decode JSON from Claude response for names: {json_string_response}")
        return [f"Error decoding JSON from Claude response: {json_string_response}"]
    except anthropic.APIError as e:
        print(f"Claude API call failed for name generation: Status Code: {e.status_code}, Response: {e.response}")
        return [f"Error calling Claude API: {e}"]
    except Exception as e:
        print(f"Error generating names with Claude: {e}")
        return [f"Error generating names: {e}"]

def save_to_local_file(filename: str, content: any):
    """
    Saves content to a local file within a 'results' directory.
    Handles different content types (dict, list, str).
    Ensures appropriate file extension for JSON content.
    """
    results_dir = "results" # Using a generic 'results' directory for a simpler script
    os.makedirs(results_dir, exist_ok=True)
    filepath = os.path.join(results_dir, filename)
    try:
        mode = "w"
        content_to_write = ""

        if isinstance(content, (dict, list)):
            content_to_write = json.dumps(content, indent=2)
            # Ensure .json extension if content is dict or list
            if not filepath.lower().endswith(".json"):
                filepath = os.path.splitext(filepath)[0] + ".json"
        else:
            content_to_write = str(content)
        
        with open(filepath, mode, encoding="utf-8") as f:
            f.write(content_to_write)
        print(f"Content successfully saved to {filepath}")
        return f"File saved: {filepath}"
    except IOError as e:
        print(f"Error saving file {filepath}: {e}")
        return f"Error saving file: {e}"

# --- Main Execution Flow ---
if __name__ == "__main__":
    print("## Starting Simple Claude Branding Script ##")

    # Define the business idea
    business_idea_context = "An AI-powered project management tool that automates task delegation and progress tracking for small to medium-sized teams, focusing on intuitive UX and real-time insights."

    # 1. Generate Brand Names
    print("\n--- Generating Brand Names ---")
    possible_brand_names = call_claude_name_generation_api(business_idea_context)
    save_to_local_file('possible_brand_names.json', possible_brand_names)
    
    chosen_brand_name = None
    if possible_brand_names and isinstance(possible_brand_names, list) and len(possible_brand_names) > 0:
        chosen_brand_name = possible_brand_names[0] # Simply pick the first name from the list
        print(f"Chosen Brand Name: {chosen_brand_name}")
        save_to_local_file('chosen_brand_name.txt', chosen_brand_name)
    else:
        print("Could not generate or select a brand name.")
        exit() # Exit if no brand name is chosen

    # Logomark generation removed as Claude does not have native image generation.
    print("\nLogomark generation skipped as Claude does not support image generation directly.")

    print("\n\n################################################")
    print("## Simple Claude Branding Script Finished ##")
    ################################################
    print(f"\nAll results saved in the 'results' directory.")

