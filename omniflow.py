from __future__ import annotations

import argparse
import json
import os
import random
import shutil
import subprocess
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

import requests
from anthropic import Anthropic, NotFoundError
from dotenv import load_dotenv

class OmniFlow:
    """
    A unified pipeline for SaaS ideation, branding, documentation, and project scaffolding.
    This class consolidates the logic from the four original scripts into a single,
    orchestrated workflow.
    """

    # ╔═════════════════════ 0. CONFIGURATION & INITIALIZATION ══════════════════════════════════╗
    def __init__(self, limit: int = 2, outdir: Optional[Path] = None):
        """
        Initializes the OmniFlow pipeline.

        Args:
            limit: The number of top Product Hunt launches to analyze.
            outdir: The directory to save all generated artifacts.
        """
        load_dotenv()
        self.ph_token: str | None = os.getenv("PH_TOKEN")
        self.anthropic_api_key: str | None = os.getenv("ANTHROPIC_API_KEY")
        self.pref_model: str | None = os.getenv("ANTHROPIC_MODEL")

        if not self.ph_token or not self.anthropic_api_key:
            sys.exit("❌ Set PH_TOKEN and ANTHROPIC_API_KEY in your environment or .env file")

        self.limit = limit
        self.outdir = outdir or Path.cwd() / "results"
        self.outdir.mkdir(parents=True, exist_ok=True)
        print(f"Output directory: {self.outdir.resolve()}")

        self.anthropic_client = Anthropic(api_key=self.anthropic_api_key)
        self.model_candidates = list(filter(None, [self.pref_model, "claude-3-5-sonnet-20240620", "claude-3-haiku-20240307"]))

        # --- In-memory state to pass data between stages ---
        self.raw_posts: List[Dict] = []
        self.product_analysis_content: str = ""
        self.best_hypothesis_content: str = ""
        self.brd_content: str = ""
        self.prd_content: str = ""
        self.brand_name: str = ""
        self.srs_content: str = ""

    # ╔═════════════════════ 1. STAGE 1: IDEATION (Product Analysis & Docs) ════════════════════╗
    def stage1_ideation(self):
        """
        Executes the first stage of the pipeline:
        1. Fetches top products from Product Hunt.
        2. Analyzes them to find market gaps and opportunities.
        3. Generates solution hypotheses and selects the best one.
        4. Drafts initial Business (BRD) and Product (PRD) requirements documents.
        """
        print("--- Starting Stage 1: Ideation & Requirements ---")

        # 1a. Fetch Product Hunt data
        print(f"Fetching details for top {self.limit} products from Product Hunt...")
        slugs = self._fetch_top_slugs(self.limit)
        self.raw_posts = [self._fetch_post(s) for s in slugs]

        # 1b. Generate initial product analysis content
        self.product_analysis_content = self._generate_raw_product_analysis(self.raw_posts)
        pa_md_path = self.outdir / "1_product_analysis.md"
        self._write_file(pa_md_path, f"# Product Analysis\n\n{self.product_analysis_content}")
        print(f"✔ Saved initial product analysis to {pa_md_path.name}")

        # 1c. LLM-powered market analysis
        print("Analyzing market for pains, trends, and gaps...")
        analyse_prompt = (
            "Based on the full product data above, for each product identify:\n"
            "- core user pain (≤15 words)\n"
            "- 1–2 macro trends\n"
            "- whitespace opportunity\n"
            "Return a Markdown table: Product | Pain | Trend | Opportunity."
        )
        market_analysis_section = self._call_llm(analyse_prompt, self.product_analysis_content)
        self._append_to_file(pa_md_path, "Market Analysis (Pains · Trends · Gaps)", market_analysis_section)
        self.product_analysis_content += f"\n\n## Market Analysis\n\n{market_analysis_section}"
        print(f"✔ Appended market analysis to {pa_md_path.name}")

        # 1d. Generate and score solution hypotheses
        print("Generating and scoring SaaS solution hypotheses...")
        hypo_prompt = (
            "From the product data and analysis above, propose 5 SaaS solution hypotheses.\n"
            "For each include: a 30-word pitch, ICP, UVP, and key features."
        )
        hypotheses_text = self._call_llm(hypo_prompt, self.product_analysis_content)

        score_prompt = (
            "Score each hypothesis 1–5 on pain severity, TAM, whitespace, and feasibility.\n"
            "Return a Markdown table with total scores and recommend the best."
        )
        scored_hypotheses_text = self._call_llm(score_prompt, hypotheses_text)
        
        sol_md_path = self.outdir / "2_solution_hypotheses.md"
        full_solution_content = f"{hypotheses_text}\n\n## Scored Hypotheses\n\n{scored_hypotheses_text}"
        self._write_file(sol_md_path, f"# SaaS Solution Hypotheses\n\n{full_solution_content}")
        print(f"✔ Saved scored hypotheses to {sol_md_path.name}")

        # 1e. Select the best hypothesis
        print("Selecting the best hypothesis...")
        select_prompt = (
            "From the above table and hypotheses, output ONLY the single highest-scoring "
            "hypothesis as plain text, including its full 30-word pitch, ICP, UVP, and key features."
        )
        self.best_hypothesis_content = self._call_llm(select_prompt, full_solution_content, max_tokens=300)
        best_md_path = self.outdir / "3_best_hypothesis.md"
        self._write_file(best_md_path, f"# Selected Hypothesis\n\n{self.best_hypothesis_content}")
        print(f"✔ Saved best hypothesis to {best_md_path.name}")

        # 1f. Draft BRD
        print("Drafting Business Requirements Document (BRD)...")
        brd_prompt = (
            "Using the selected hypothesis below, write a concise BRD (≤500 words) including:\n"
            "- background\n- problem statement\n- goals\n- non-goals\n- success metrics\n- personas"
        )
        self.brd_content = self._call_llm(brd_prompt, self.best_hypothesis_content, max_tokens=800)
        brd_md_path = self.outdir / "4_brd.md"
        self._write_file(brd_md_path, f"# Business Requirements Document (BRD)\n\n{self.brd_content}")
        print(f"✔ Saved BRD to {brd_md_path.name}")

        # 1g. Draft PRD
        print("Drafting Product Requirements Document (PRD)...")
        prd_prompt = (
            "Using the BRD and the selected hypothesis below, draft a PRD (≤700 words) with sections:\n"
            "- overview\n- feature list\n- user flows\n- functional requirements\n- KPIs\n- phased roadmap"
        )
        prd_ctx = self.best_hypothesis_content + "\n\n" + self.brd_content
        self.prd_content = self._call_llm(prd_prompt, prd_ctx, max_tokens=1000)
        prd_md_path = self.outdir / "5_prd.md"
        self._write_file(prd_md_path, f"# Product Requirements Document (PRD)\n\n{self.prd_content}")
        print(f"✔ Saved PRD to {prd_md_path.name}")
        print("--- Stage 1 Complete ---")

    # ╔═════════════════════ 2. STAGE 2: BRANDING ═══════════════════════════════════════════════╗
    def stage2_branding(self):
        """
        Executes the second stage:
        1. Generates potential brand names based on the business idea.
        2. Selects the best name.
        """
        print("\n--- Starting Stage 2: Branding ---")
        if not self.best_hypothesis_content:
            sys.exit("❌ Cannot run Stage 2 without a business hypothesis from Stage 1.")

        print("Generating brand names...")
        name_prompt = (
            f"Generate a JSON array of 10 unique, creative, and relevant brand names for a "
            f"SaaS business based on this idea: {self.best_hypothesis_content}. "
            f"The JSON should contain only the array of strings, like [\"Name1\", \"Name2\", ...]."
            f"\n\nRespond only with the JSON array."
        )
        
        try:
            response_text = self._call_llm(name_prompt, max_tokens=1000)
            possible_names = json.loads(response_text)
            if not isinstance(possible_names, list) or not all(isinstance(n, str) for n in possible_names):
                raise ValueError("LLM did not return a list of strings.")
        except (json.JSONDecodeError, ValueError) as e:
            print(f"⚠️ Could not parse brand names from LLM response: {e}. Falling back to a default name.")
            possible_names = ["InnovateFlow", "TaskForge", "ProjectSphere"]

        self.brand_name = possible_names[0]
        
        names_path = self.outdir / "6_possible_brand_names.json"
        self._write_file(names_path, json.dumps(possible_names, indent=2))
        print(f"✔ Saved possible brand names to {names_path.name}")

        brand_name_path = self.outdir / "7_chosen_brand_name.txt"
        self._write_file(brand_name_path, self.brand_name)
        print(f"✔ Chosen Brand Name: '{self.brand_name}' (saved to {brand_name_path.name})")
        print("--- Stage 2 Complete ---")

    # ╔═════════════════════ 3. STAGE 3: TECHNICAL DOCUMENTATION ═══════════════════════════════╗
    def stage3_documentation(self):
        """
        Executes the third stage:
        1. Generates SRS (Software Requirements Specification).
        2. Generates System Architecture document.
        3. Generates User Flow diagram (in Mermaid syntax).
        """
        print("\n--- Starting Stage 3: Technical Documentation ---")
        if not all([self.brd_content, self.prd_content, self.brand_name]):
            sys.exit("❌ Cannot run Stage 3 without BRD, PRD, and Brand Name from previous stages.")

        docs_to_generate = {
            "Software Requirements Specification (SRS)": "8_srs.md",
            "System Architecture": "9_system_architecture.md",
            "User Flow (in Mermaid syntax)": "10_user_flow.md",
        }

        for doc_type, filename in docs_to_generate.items():
            print(f"Generating {doc_type}...")
            prompt = (
                f"**Brand Name:** {self.brand_name}\n\n"
                f"**Business Requirements Document (BRD) Content:**\n{self.brd_content}\n\n"
                f"**Product Requirements Document (PRD) Content:**\n{self.prd_content}\n\n"
                f"Based on the above information, please generate ONLY the following document: **{doc_type}**.\n"
                f"Do not include any other documents or introductory text."
            )
            
            content = self._call_llm(prompt, max_tokens=4000)
            doc_path = self.outdir / filename
            self._write_file(doc_path, content)
            
            if "SRS" in doc_type:
                self.srs_content = content # Save for stage 4

            print(f"✔ Saved {doc_type} to {doc_path.name}")
        
        print("--- Stage 3 Complete ---")

    # ╔═════════════════════ 4. STAGE 4: MVP SCAFFOLDING ═══════════════════════════════════════╗
    def stage4_scaffolding(self):
        """
        Executes the final stage:
        1. Creates a new Next.js project.
        2. Copies the SRS document into the project.
        3. Creates a comprehensive context file for AI-driven development.
        """
        print("\n--- Starting Stage 4: MVP Development Setup ---")
        if not self.brand_name:
            sys.exit("❌ Cannot run Stage 4 without a brand name from Stage 2.")

        project_name = self.brand_name.lower().replace(' ', '_').replace('-', '_')
        project_path = Path.cwd() / project_name

        print(f"Project will be created at: {project_path.resolve()}")

        # 4a. Create Next.js app
        self._create_next_app(project_path)

        # 4b. Copy SRS file
        srs_dest_path = project_path / 'srs.md'
        print(f"Copying SRS to {srs_dest_path}...")
        self._write_file(srs_dest_path, self.srs_content)
        print("✔ SRS copied.")

        # 4c. Create full context file
        print("Creating GEMINI_CONTEXT.md file...")
        context_content = f"# Project Context: {self.brand_name}\n\nThis document contains all the necessary information to develop the MVP for {self.brand_name}.\n\n## 1. Brand Name\n{self.brand_name}\n\n## 2. Business Requirements Document (BRD)\n{self.brd_content}\n\n## 3. Product Requirements Document (PRD)\n{self.prd_content}\n\n## 4. Software Requirements Specification (SRS)\n{self.srs_content}"
        context_file_path = project_path / 'GEMINI_CONTEXT.md'
        self._write_file(context_file_path, context_content)
        print(f"✔ GEMINI_CONTEXT.md created in {project_path.name}")
        print("--- Stage 4 Complete ---")
        print(f"\nNext steps: Navigate to the new project directory and start development:")
        print(f"cd {project_path.name}")

    # ╔═════════════════════ PRIVATE HELPERS: APIs ════════════════════════════════════════════╗
    def _gql_request(self, query: str, variables: Dict | None = None, max_retries: int = 5) -> Dict:
        """POST to Product Hunt GraphQL with exponential 429 back-off."""
        endpoint = "https://api.producthunt.com/v2/api/graphql"
        headers = {"Authorization": f"Bearer {self.ph_token}"}
        attempt, wait = 0, 2
        while True:
            try:
                r = requests.post(endpoint,
                                  json={"query": query, "variables": variables or {}},
                                  headers=headers, timeout=30)
                if r.status_code == 429 and attempt < max_retries:
                    sleep_for = wait + random.random()
                    print(f"⏳ Rate-limited; sleeping {sleep_for:.1f}s…")
                    time.sleep(sleep_for)
                    attempt += 1
                    wait = min(wait * 2, 60)
                    continue
                r.raise_for_status()
                data = r.json()
                if "errors" in data:
                    raise RuntimeError(str(data["errors"]))
                return data["data"]
            except (requests.RequestException, RuntimeError) as e:
                print(f"GraphQL request failed: {e}")
                sys.exit(1)

    def _call_llm(self, prompt: str, context: str = "", max_tokens: int = 2000) -> str:
        """Single call to the Anthropic API with model fallbacks."""
        messages = [{"role": "user", "content": f"{prompt}\n\n{context}" if context else prompt}]
        last_err: Optional[Exception] = None
        for model in self.model_candidates:
            try:
                resp = self.anthropic_client.messages.create(
                    model=model,
                    max_tokens=max_tokens,
                    temperature=0.7,
                    system="You are a pragmatic and creative SaaS strategist and engineer.",
                    messages=messages,
                )
                return resp.content[0].text.strip()
            except NotFoundError as e:
                print(f"⚠️ Model {model} not found, trying next...")
                last_err = e
                continue
            except Exception as e:
                print(f"API call with {model} failed: {e}")
                last_err = e
                continue
        raise RuntimeError("All Anthropic models failed.") from last_err

    def _fetch_top_slugs(self, limit: int) -> List[str]:
        """Return the first `limit` launch slugs from the Product Hunt ranking feed."""
        query = """
        query Launches($after: String) {
          posts(order: RANKING, first: 40, after: $after) {
            pageInfo { endCursor hasNextPage }
            edges    { node { slug } }
          }
        }
        """
        slugs, cursor = [], None
        while len(slugs) < limit:
            block = self._gql_request(query, {"after": cursor})["posts"]
            slugs.extend(edge["node"]["slug"] for edge in block["edges"])
            if not block["pageInfo"]["hasNextPage"]:
                break
            cursor = block["pageInfo"]["endCursor"]
        return slugs[:limit]

    def _fetch_post(self, slug: str) -> Dict:
        """Return full metadata (including comments) for a single launch."""
        query = """
        query PostDetails($slug: String!) {
          post(slug: $slug) {
            id name slug tagline description url website createdAt
            votesCount commentsCount reviewsRating
            topics(first: 10) { edges { node { name } } }
            makers           { username name }
            media            { url type }
            comments(first: 20) { edges { node { id body createdAt user { username name } } } }
          }
        }
        """
        post = self._gql_request(query, {"slug": slug})["post"]
        comments = [
            {
                "id": c["node"]["id"], "body": c["node"]["body"],
                "when": c["node"]["createdAt"], "user": c["node"]["user"]["username"],
            }
            for c in post.get("comments", {}).get("edges", [])
        ]
        post["comments_data"] = comments
        return post

    # ╔═════════════════════ PRIVATE HELPERS: UTILITIES ═══════════════════════════════════════╗
    @staticmethod
    def _esc(val: Any) -> str:
        """Markdown-escape pipes & newlines; stringify collections."""
        if not val: return "—"
        if isinstance(val, (list, tuple, set)): val = ", ".join(map(str, val))
        return str(val).replace("\n", " ").replace("|", "\\|").strip()

    @staticmethod
    def _write_file(path: Path, content: str):
        """Writes content to a file."""
        try:
            path.write_text(content, encoding="utf-8")
        except IOError as e:
            print(f"Error writing to file {path}: {e}")
            sys.exit(1)

    def _append_to_file(self, path: Path, title: str, content: str):
        """Appends a new section to an existing file."""
        try:
            with path.open("a", encoding="utf-8") as f:
                f.write(f"\n## {title}\n\n{content}\n")
        except IOError as e:
            print(f"Error appending to file {path}: {e}")
            sys.exit(1)

    def _generate_raw_product_analysis(self, posts: List[Dict]) -> str:
        """Generates a Markdown string with the raw data of all fetched products."""
        lines: List[str] = []
        for p in posts:
            lines.extend([
                f"## {p['name']}",
                f"Slug: {p['slug']}",
                f"URL: {p['url']}",
                f"Website: {self._esc(p['website'])}",
                f"Created: {p['createdAt']}",
                f"Tagline: {self._esc(p['tagline'])}",
                f"Description: {self._esc(p['description'])}",
                f"Votes: {p['votesCount']} · Comments: {p['commentsCount']} · Rating: {p['reviewsRating']}",
                f"Topics: {self._esc([t['node']['name'] for t in p['topics']['edges']])}",
                f"Makers: {self._esc([m['username'] for m in p['makers']])}",
            ])
            if p.get("media"):
                lines.append(f"Media: {self._esc([m['url'] for m in p['media']])}")
            if p.get("comments_data"):
                lines.append("### Comments")
                for c in p["comments_data"][:5]:
                    lines.append(f"- **{c['user']}** at {c['when']}: {self._esc(c['body'])}")
            lines.append("")
        return "\n".join(lines)

    @staticmethod
    def _create_next_app(project_path: Path):
        """Creates a new Next.js app using a non-interactive command."""
        if project_path.exists():
            print(f"⚠️ Directory '{project_path.name}' already exists. Skipping Next.js app creation.")
            return

        parent_dir = project_path.parent
        project_name = project_path.name
        print(f"Creating Next.js app: {project_name} in {parent_dir}...")
        
        # Add --no-turbopack to prevent interactive prompts that hang the script.
        command_list = [
            "npx", "create-next-app@latest", project_name,
            "--ts", "--tailwind", "--eslint", "--app", 
            "--import-alias", "@/", "--no-src-dir", "--use-npm",
            "--no-turbopack"
        ]
        command_str = " ".join(command_list)
        
        try:
            # The command should be fully non-interactive with the flags above.
            # No 'input' is needed.
            result = subprocess.run(
                command_str, 
                shell=True, 
                cwd=parent_dir, 
                text=True, 
                capture_output=True
            )

            if result.returncode != 0 or not project_path.exists() or not project_path.is_dir():
                print(f"❌ Failed to create Next.js project directory '{project_path.name}'.")
                print(f"Return Code: {result.returncode}")
                if result.stdout:
                    print(f"--- STDOUT ---\n{result.stdout.strip()}")
                if result.stderr:
                    print(f"--- STDERR ---\n{result.stderr.strip()}")
                print("Please ensure Node.js, npm, and npx are installed and in your PATH.")
                sys.exit(1)

            print("✔ Next.js app created successfully.")
        except FileNotFoundError:
            print("❌ 'npx' command not found. Please ensure Node.js is installed and in your PATH.")
            sys.exit(1)
        except Exception as e:
            print(f"❌ An unexpected error occurred during Next.js app creation: {e}")
            sys.exit(1)

    # ╔═════════════════════ PUBLIC RUN METHOD ═════════════════════════════════════════════════╗
    def run(self):
        """Executes the full pipeline from start to finish."""
        start_time = time.time()
        self.stage1_ideation()
        self.stage2_branding()
        self.stage3_documentation()
        self.stage4_scaffolding()
        end_time = time.time()
        print(f"\n✨ OmniFlow complete in {end_time - start_time:.2f} seconds. ✨")

# ╔═════════════════════ CLI ENTRYPOINT ═════════════════════════════════════╗
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="OmniFlow: A unified SaaS ideation and scaffolding pipeline.")
    parser.add_argument("--limit", type=int, default=2,
                        help="Number of Product Hunt launches to analyze (default: 2).")
    parser.add_argument("--outdir", type=Path,
                        help="Output directory for all generated files (default: ./results).")
    args = parser.parse_args()

    pipeline = OmniFlow(limit=args.limit, outdir=args.outdir)
    pipeline.run()
