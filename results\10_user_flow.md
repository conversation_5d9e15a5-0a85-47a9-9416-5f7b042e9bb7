```mermaid
graph TD
    A[Start] --> B[User inputs prompt]
    B --> C[System analyzes prompt]
    C --> D[User receives analysis and suggestions]
    D --> E{Apply changes?}
    E -->|Yes| F[User applies suggested changes]
    E -->|No| G[User makes manual edits]
    F --> H[System re-analyzes updated prompt]
    G --> H
    H --> I[User saves optimized prompt to library]
    I --> J[End]

    K[Start] --> L[User selects prompt from library]
    L --> M[User chooses AI models for comparison]
    M --> N[System tests prompt across models]
    N --> O[User views performance comparison]
    O --> P[User makes adjustments based on insights]
    P --> Q[System updates comparison results]
    Q --> R[End]

    S[Start] --> T[User creates prompt variations]
    T --> U[User sets A/B test parameters]
    U --> V[System runs A/B test]
    V --> W[User receives test results]
    W --> X[User selects winning variation]
    X --> Y[System updates prompt library]
    Y --> Z[End]
```