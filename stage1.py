from __future__ import annotations

import argparse
import os
import random
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

import requests
from dotenv import load_dotenv
from anthropic import Anthropic, NotFoundError

# ╔═════════════════════ 0. ENV & DEFAULTS ══════════════════════════════════╗
load_dotenv()

PH_TOKEN: str | None         = os.getenv("PH_TOKEN")
ANTHROPIC_API_KEY: str | None = os.getenv("ANTHROPIC_API_KEY")
PREF_MODEL: str | None       = os.getenv("ANTHROPIC_MODEL")

DEFAULT_LIMIT  = 2
DEFAULT_OUTDIR = Path.cwd() / "results"

if not PH_TOKEN or not ANTHROPIC_API_KEY:
    sys.exit("❌  Set PH_TOKEN and ANTHROPIC_API_KEY in your environment or .env file")

# ╔═════════════════════ 1. PRODUCT HUNT GRAPHQL ════════════════════════════╗
PH_ENDPOINT = "https://api.producthunt.com/v2/api/graphql"
HEADERS     = {"Authorization": f"Bearer {PH_TOKEN}"}

POSTS_Q = """
query Launches($after: String) {
  posts(order: RANKING, first: 40, after: $after) {
    pageInfo { endCursor hasNextPage }
    edges    { node { slug } }
  }
}
"""

DETAILS_Q = """
query PostDetails($slug: String!) {
  post(slug: $slug) {
    id name slug tagline description url website createdAt
    votesCount commentsCount reviewsRating
    topics(first: 10) { edges { node { name } } }
    makers           { username name }
    media            { url type }
    comments(first: 20) {
      edges {
        node {
          id
          body
          createdAt
          user { username name }
        }
      }
    }
  }
}
"""

def gql(query: str, variables: Dict | None = None, *, max_retries: int = 5) -> Dict:
    """POST to Product Hunt GraphQL with exponential 429 back-off."""
    attempt, wait = 0, 2
    while True:
        r = requests.post(PH_ENDPOINT,
                          json={"query": query, "variables": variables or {}},
                          headers=HEADERS, timeout=30)
        if r.status_code == 429 and attempt < max_retries:
            sleep_for = wait + random.random()
            print(f"⏳ Rate-limited; sleeping {sleep_for:.1f}s …")
            time.sleep(sleep_for)
            attempt += 1
            wait = min(wait * 2, 60)
            continue
        r.raise_for_status()
        data = r.json()
        if "errors" in data:
            raise RuntimeError(data["errors"])
        return data["data"]

def fetch_top_slugs(limit: int) -> List[str]:
    """Return the first `limit` launch slugs from the ranking feed."""
    slugs, cursor = [], None
    while len(slugs) < limit:
        block = gql(POSTS_Q, {"after": cursor})["posts"]
        slugs.extend(edge["node"]["slug"] for edge in block["edges"])
        if not block["pageInfo"]["hasNextPage"]:
            break
        cursor = block["pageInfo"]["endCursor"]
    return slugs[:limit]

def fetch_post(slug: str) -> Dict:
    """Return full metadata (including comments) for a single launch."""
    post = gql(DETAILS_Q, {"slug": slug})["post"]
    comments = [
        {
            "id":   c["node"]["id"],
            "body": c["node"]["body"],
            "when": c["node"]["createdAt"],
            "user": c["node"]["user"]["username"],
        }
        for c in post.get("comments", {}).get("edges", [])
    ]
    post["comments_data"] = comments
    return post

# ╔═════════════════════ 2. FILE HELPERS ════════════════════════════════════╗
def esc(val) -> str:
    """Markdown-escape pipes & newlines; stringify collections."""
    if not val:
        return "—"
    if isinstance(val, (list, tuple, set)):
        val = ", ".join(map(str, val))
    return str(val).replace("\n", " ").replace("|", "\\|").strip()

def write_md(path: Path, title: str, body: str) -> None:
    path.write_text(f"# {title}\n\n{body}", encoding="utf-8")

def append_llm_section(path: Path, section_title: str, prompt: str,
                       max_tokens: int = 900) -> None:
    """Read existing MD, feed to LLM with prompt, append ## section_title + reply."""
    context = path.read_text(encoding="utf-8")
    reply   = call_llm(prompt, context, max_tokens=max_tokens)
    with path.open("a", encoding="utf-8") as f:
        f.write(f"\n## {section_title}\n\n{reply}\n")

def save_raw_products(posts: List[Dict], path: Path) -> None:
    """Write all raw product data (including comments) to product_analysis.md."""
    lines: List[str] = ["# Product Analysis", ""]
    for p in posts:
        lines += [
            f"## {p['name']}",
            f"Slug: {p['slug']}",
            f"URL: {p['url']}",
            f"Website: {esc(p['website'])}",
            f"Created: {p['createdAt']}",
            f"Tagline: {esc(p['tagline'])}",
            f"Description: {esc(p['description'])}",
            f"Votes: {p['votesCount']} · Comments Count: {p['commentsCount']} · Rating: {p['reviewsRating']}",
            f"Topics: {esc([t['node']['name'] for t in p['topics']['edges']])}",
            f"Makers: {esc([m['username'] for m in p['makers']])}",
        ]
        if p.get("media"):
            lines.append(f"Media: {esc([m['url'] for m in p['media']])}")
        if p.get("comments_data"):
            lines.append("### Comments")
            for c in p["comments_data"][:5]:
                lines.append(f"- **{c['user']}** at {c['when']}: {esc(c['body'])}")
        lines.append("")  # blank line
    path.write_text("\n".join(lines), encoding="utf-8")

# ╔═════════════════════ 3. ANTHROPIC WRAPPER ═══════════════════════════════╗
MODEL_CANDIDATES = [PREF_MODEL, "claude-3-5-haiku-latest"]
client = Anthropic(api_key=ANTHROPIC_API_KEY)

def call_llm(prompt: str, context: str = "", max_tokens: int = 900) -> str:
    """Single call with model fallbacks."""
    msgs = [{"role": "user", "content": f"{prompt}\n\n{context}"}]
    last_err: Optional[Exception] = None
    for m in filter(None, MODEL_CANDIDATES):
        try:
            resp = client.messages.create(
                model=m,
                max_tokens=max_tokens,
                temperature=0.7,
                system="You are a pragmatic SaaS strategist.",
                messages=msgs,
            )
            return resp.content[0].text.strip()
        except NotFoundError as e:
            last_err = e
            continue
    raise RuntimeError("No Anthropic model available") from last_err

# ╔═════════════════════ 4. STAGE-1 PIPELINE ════════════════════════════════╗
def stage1_flow(limit: int, outdir: Path) -> None:
    outdir.mkdir(parents=True, exist_ok=True)

    pa_md   = outdir / "product_analysis.md"
    sol_md  = outdir / "solution_hypotheses.md"
    best_md = outdir / "best_hypothesis.md"
    brd_md  = outdir / "brd.md"
    prd_md  = outdir / "prd.md"

    # 1. Scrape raw products
    slugs     = fetch_top_slugs(limit)
    raw_posts = [fetch_post(s) for s in slugs]
    save_raw_products(raw_posts, pa_md)

    # 2. Analyse pains / trends / gaps (append to product_analysis.md)
    analyse_prompt = (
        "Based on the full product data above, for each product identify:\n"
        "- core user pain (≤15 words)\n"
        "- 1–2 macro trends\n"
        "- whitespace opportunity\n"
        "Return a Markdown table: Product | Pain | Trend | Opportunity."
    )
    append_llm_section(pa_md, "Market Analysis (Pains · Trends · Gaps)", analyse_prompt)

    # 3. Generate 5 raw SaaS solution hypotheses → solution_hypotheses.md
    hypo_prompt = (
        "From the product data and analysis above, propose 5 SaaS solution hypotheses.\n"
        "For each include:\n"
        "- 30-word pitch\n"
        "- ICP\n"
        "- UVP\n"
        "- key features"
    )
    sol_text = call_llm(hypo_prompt, pa_md.read_text(encoding="utf-8"))
    write_md(sol_md, "SaaS Solution Hypotheses", sol_text)

    # 4. Score those hypotheses, append scoring table into solution_hypotheses.md
    score_prompt = (
        "Score each hypothesis 1–5 on pain severity, TAM, whitespace, feasibility.\n"
        "Return a Markdown table with total scores and recommend the best."
    )
    append_llm_section(sol_md, "Scored Hypotheses", score_prompt)

    # 5. Extract the single best hypothesis (full spec) → best_hypothesis.md
    select_prompt = (
        "From the above table and hypotheses, output ONLY the single highest-scoring "
        "hypothesis as plain text, including its full 30-word pitch, ICP, UVP, and key features."
    )
    best_text = call_llm(select_prompt, sol_md.read_text(encoding="utf-8"), max_tokens=300)
    write_md(best_md, "Selected Hypothesis", best_text)

    # 6. Draft BRD → context = best_hypothesis.md
    brd_prompt = (
        "Using the selected hypothesis below, write a concise BRD (≤500 words) including:\n"
        "- background\n- problem statement\n- goals\n- non-goals\n- success metrics\n- personas"
    )
    brd_text = call_llm(brd_prompt, best_md.read_text(encoding="utf-8"), max_tokens=800)
    write_md(brd_md, "Business Requirements Document (BRD)", brd_text)

    # 7. Draft PRD → context = best_hypothesis.md + brd.md
    prd_prompt = (
        "Using the BRD and the selected hypothesis below, draft a PRD (≤700 words) with sections:\n"
        "- overview\n- feature list\n- user flows\n- functional requirements\n- KPIs\n- phased roadmap"
    )
    prd_ctx  = best_md.read_text(encoding="utf-8") + "\n\n" + brd_md.read_text(encoding="utf-8")
    prd_text = call_llm(prd_prompt, prd_ctx, max_tokens=1000)
    write_md(prd_md, "Product Requirements Document (PRD)", prd_text)

    print("✔ Done - generated:")
    print("  •", pa_md)
    print("  •", sol_md)
    print("  •", best_md)
    print("  •", brd_md)
    print("  •", prd_md)

# ╔═════════════════════ 5. CLI ENTRYPOINT ═══════════════════════════════════╗
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Stage-1 SaaS ideation pipeline")
    parser.add_argument("--limit", type=int, default=DEFAULT_LIMIT,
                        help=f"number of launches to scrape (default {DEFAULT_LIMIT})")
    parser.add_argument("--outdir", type=Path, default=DEFAULT_OUTDIR,
                        help="output directory")
    args = parser.parse_args()

    stage1_flow(args.limit, args.outdir)
